import { Address } from "cluster";
import { Base, Billing, Contact, Image, Settle, Tag } from "./CustomerSaveEntity";

export interface CustomerItemRecordType {
    /**
   * 客户地址信息
   */
    addresses?: Address[];
    /**
     * 客户基础信息
     */
    base?: Base;
    /**
     * 客户开票信息
     */
    billings?: Billing[];
    /**
     * 客户联系人信息
     */
    contacts?: Contact[];
    /**
     * 客户开票信息
     */
    images?: Image[];
    /**
     * 客户结算信息
     */
    settle?: Settle;
    /**
     * 客户标签信息
     */
    tags?: Tag[];
}