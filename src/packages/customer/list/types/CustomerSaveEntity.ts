import { AdvanceAmountList, PositionList, ReceivableAmountList } from "@/components/ChooseCstModal/types/CustomerEntity";
import { CustomerAddressEntity } from "./CustomerAddressEntity";
import { CustomerContactEntity } from "./CustomerContactEntity";
import { CustomerImageEntity } from "./CustomerImageEntiry";
import { CustomerTagEntity } from "./CustomerTagEntity";
export interface Contact {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 邮箱地址
   */
  email?: string;
  /**
   * 联系人姓名
   */
  firstName?: string;
  /**
   * 是否开通商城权限
   */
  hasMallPermission?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * 1:默认联系人0:非默认联系人
   */
  isDefault?: number;
  /**
   * 联系人姓名
   */
  lastName?: string;
  /**
   * 联系人电话
   */
  phone?: string;
  /**
   * 职务
   */
  position?: string;
  /**
   * 职务
   */
  positionList?: PositionList[];
  /**
   * QQ号码
   */
  qq?: string;
  /**
   * 客户备注
   */
  remark?: string;
  /**
   * 微信号
   */
  wechat?: string;
}
export interface Tag {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 标签ID
   */
  tagId?: string;
  /**
   * 标签名称
   */
  tagName?: string;
}

export interface Address {
  /**
   * 详细地址
   */
  address?: string;
  /**
   * 市编码
   */
  cityCode?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 联系人姓名
   */
  firstName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 是否默认地址
   */
  isDefault?: number;
  /**
   * 联系人姓名
   */
  lastName?: string;
  /**
   * 联系人电话
   */
  phone?: string;
  /**
   * 邮编
   */
  postCode?: string;
  /**
   * 区县编码
   */
  prefectureCode?: string;
  /**
   * 区县名称
   */
  prefectureName?: string;
  /**
   * 省编码
   */
  provinceCode?: string;
  /**
   * 省名称
   */
  provinceName?: string;
}


export interface Image {
  /** 图片地址 */
  url: string;
  /** 编辑传入id */
  id?: string;
  uid?: string;
}
/**
 * 客户结算信息
 */
export interface Settle {
  /**
   * 客户预收（多币种）
   */
  advanceAmountCurrency?: string;
  /**
   * 自定义list对象
   */
  advanceAmountList?: AdvanceAmountList[];
  /**
   * 可用额度
   */
  availableAmount?: number;
  /**
   * 是否挂账
   */
  credit?: boolean;
  /**
   * 信用账期（天）
   */
  creditTerms?: number;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 冻结额度
   */
  freezeAmount?: number;
  /**
   * 客户不收税，0为收税1为不收税
   */
  gstExcluded?: 0 | 1;
  /**
   * 是否多币种1=多币种0=单币种
   */
  isMultiCurrency?: number;
  /**
   * 客户应收（多币种）
   */
  receivableAmountCurrency?: string;
  /**
   * 自定义list对象
   */
  receivableAmountList?: ReceivableAmountList[];
  /**
   * 剩余账期（天）
   */
  remainTerms?: number;
  /**
   * 客户期望额度
   */
  requestCreditLimit?: number;
  /**
   * 客户期望结算方式
   */
  requestSettleType?: string;
  /**
   * 结算类型
   */
  settleType?: string;
  /**
   * 是否可用0=启用1=逾期2=停用
   */
  status?: number;
  /**
   * 状态名
   */
  statusName?: string;
  /**
   * 信用额度
   */
  totalAmount?: number;
  /**
   * 已用额度
   */
  usedAmount?: number;
}

export interface Billing {
  /**
   * 开户行账号
   */
  accountNo?: string;
  /**
   * 地址
   */
  address?: string;
  /**
   * 开户行名称
   */
  bankName?: string;
  /**
   * 开票单位
   */
  billingUnit?: string;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 电话
   */
  phone?: string;
  /**
   * 纳税识别号
   */
  taxNo?: string;
}
export interface Base {
  /** 客户ID */
  id?: string;
  /** 客户编码 */
  cstSn?: string;
  /** 客户名称 */
  cstName?: string;
  /** 客户简称 */
  nickName?: string;
  /** 归属门店ID */
  storeId?: string;
  /** 归属门店名称 */
  storeName?: string;
  /** 业务员ID */
  salesmanId?: string;
  /** 业务员名 */
  salesmanName?: string;
  /** 客户状态: 0=启用 1=禁用 */
  cstStatus: number;
  /** 客户备注 */
  remark?: string;
  createTime?: string;
}

export interface CustomerSaveEntity {
  base?: Base;
  /** 联系人列表 */
  contacts?: CustomerContactEntity[];
  /** 客户标签列表 */
  tags?: CustomerTagEntity[];
  tagIdList?: string[];
  /** 客户地址列表 */
  addresses?: CustomerAddressEntity[];
  /** 客户图片列表 */
  images?: CustomerImageEntity[];
  /** 挂账信息 */
  settle?: Settle;
}

export interface Image {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 图片地址
   */
  url?: string;
}